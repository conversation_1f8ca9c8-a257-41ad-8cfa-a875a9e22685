# ButtonRender 组件使用指南

## 概述

ButtonRender 组件已经重构，现在支持两种模式：

1. **传统按钮模式** - 原有的多按钮显示方式
2. **下拉菜单模式** - 主要按钮 + 下拉菜单的组合方式

## 主要改进

### 1. 支持多个主要按钮

下拉模式现在支持多个主要操作按钮，不再限制为单个按钮。

### 2. 统一的权限和显示控制

所有按钮（主要按钮和下拉菜单项）都支持：

- `auth` - 权限控制
- `show` - 显示条件控制
- `disabled` - 禁用状态控制

### 3. 简化的配置接口

- `DropdownConfig.primaryAction` 改为 `primaryActions`（数组）
- 自动过滤不可见的按钮和菜单项

## 使用方式

### 传统按钮模式（保持不变）

```typescript
// 在 AG-Grid 列定义中
{
  field: 'actions',
  headerName: '操作',
  cellRenderer: ButtonRender,
  cellRendererParams: {
    actions: [
      {
        label: '编辑',
        callback: (data) => console.log('编辑', data),
        type: 'primary',
        auth: ['edit']
      },
      {
        label: '删除',
        callback: (data) => console.log('删除', data),
        type: 'danger',
        auth: ['delete'],
        popConfirm: {
          title: '确定删除吗？'
        }
      }
    ]
  }
}
```

### 下拉菜单模式（新功能）

#### 方式一：直接使用 actions（推荐）

```typescript
// 在 AG-Grid 列定义中 - 简化写法
{
  field: 'actions',
  headerName: '操作',
  cellRenderer: ButtonRender,
  cellRendererParams: {
    actions: {
      label: '操作', // 下拉按钮文本，默认"更多"
      size: 'small', // 按钮大小

      // 主要操作按钮（支持多个）
      primaryActions: [
        {
          label: '编辑',
          callback: (data) => console.log('编辑', data),
          type: 'primary',
          auth: ['edit']
        },
        {
          label: '查看',
          callback: (data) => console.log('查看', data),
          type: 'default',
          auth: ['view']
        }
      ],

      // 下拉菜单项
      menuItems: [
        {
          label: '复制',
          callback: (data) => console.log('复制', data),
          auth: ['copy']
        },
        {
          label: '导出',
          callback: (data) => console.log('导出', data),
          auth: ['export'],
          divided: true // 显示分割线
        },
        {
          label: '删除',
          callback: (data) => console.log('删除', data),
          auth: ['delete'],
          popConfirm: {
            title: '确定删除吗？'
          }
        }
      ]
    }
  }
}
```

#### 方式二：使用 dropdown 配置（向后兼容）

```typescript
// 在 AG-Grid 列定义中 - 原有写法仍然支持
{
  field: 'actions',
  headerName: '操作',
  cellRenderer: ButtonRender,
  cellRendererParams: {
    dropdown: {
      // 配置内容同上...
    }
  }
}
```

## Action 接口说明

```typescript
interface Action {
  label: string | ((params: ICellRendererParams) => string); // 按钮文本
  callback: Function; // 点击回调
  auth?: string[]; // 权限代码数组
  type?: string | ((params: ICellRendererParams) => string); // 按钮类型
  size?: 'small' | 'default' | 'large'; // 按钮大小
  class?: string | ((params: ICellRendererParams) => string); // 自定义样式类
  eventName?: string; // 事件名称
  popConfirm?: PopConfirm; // 确认弹窗配置
  disabled?: boolean | ((params: ICellRendererParams) => boolean); // 禁用状态
  show?: boolean | ((data: any) => boolean); // 显示条件
  divided?: boolean; // 下拉菜单项分割线
}
```

## DropdownConfig 接口说明

```typescript
interface DropdownConfig {
  label?: string; // 下拉按钮文本，默认"更多"
  size?: 'small' | 'default' | 'large'; // 下拉按钮大小
  primaryActions?: Action[]; // 主要操作按钮数组
  menuItems: Action[]; // 下拉菜单项数组
}
```

## 动态配置示例

```typescript
// 根据数据动态配置按钮
{
  field: 'actions',
  headerName: '操作',
  cellRenderer: ButtonRender,
  cellRendererParams: {
    dropdown: {
      primaryActions: [
        {
          label: (params) => params.data.status === 'active' ? '停用' : '启用',
          callback: (data) => toggleStatus(data),
          type: (params) => params.data.status === 'active' ? 'warning' : 'success',
          auth: ['status-control']
        }
      ],
      menuItems: [
        {
          label: '编辑',
          callback: (data) => editItem(data),
          auth: ['edit'],
          show: (data) => data.editable // 只有可编辑的项目才显示编辑按钮
        },
        {
          label: '删除',
          callback: (data) => deleteItem(data),
          auth: ['delete'],
          disabled: (params) => params.data.protected, // 受保护的项目禁用删除
          popConfirm: {
            title: '确定删除吗？',
            description: '删除后无法恢复'
          }
        }
      ]
    }
  }
}
```

## 您的代码简化示例

### 之前的写法

```typescript
dropdown: {
  label: '...',
  size: 'small',
  primaryActions: [/* ... */],
  menuItems: [/* ... */]
}
```

### 现在的简化写法

```typescript
actions: {
  label: '...',
  size: 'small',
  primaryActions: [
    {
      label: $t('basic.view'),
      callback: (data: any) => handleEdit(data, 'view'),
      auth: ['purchase.local.view'],
      type: 'primary',
      size: 'small',
    },
    {
      label: $t('purchase.confirmOrder'),
      callback: (data: any) => handleConfirmOrder(data),
      auth: ['purchase.local.confirm'],
      type: 'success',
      size: 'small',
      show: (data: any) => data.orderStatus === 'new',
    },
    {
      label: $t('purchase.cancelConfirm'),
      callback: (data: any) => handleCancelConfirm(data),
      auth: ['purchase.local.cancelConfirm'],
      type: 'warning',
      size: 'small',
      show: (data: any) => data.orderStatus === 'confirm',
    }
  ],
  menuItems: [
    {
      label: $t('basic.delete'),
      callback: (data: any) => handleDelete(data),
      auth: ['purchase.local.delete'],
      type: 'danger',
      size: 'small',
      divided: true,
    },
  ],
}
```

## 注意事项

1. **向后兼容**：原有的 `actions` 数组和 `dropdown` 配置方式完全保持不变
2. **自动检测**：组件会自动检测配置类型，无需手动指定模式
3. **权限控制**：所有按钮都会自动根据 `auth` 数组进行权限检查
4. **显示控制**：使用 `show` 属性可以根据数据动态控制按钮显示
5. **性能优化**：组件会自动过滤不可见的按钮，避免不必要的渲染
